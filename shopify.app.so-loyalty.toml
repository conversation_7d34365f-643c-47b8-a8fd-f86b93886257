# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "8e6bc5e5733e7ec7b544d2be3e1ade22"
name = "so-loyalty"
handle = "so-loyalty"
application_url = "https://comfort-symposium-sun-geneva.trycloudflare.com"
embedded = true

[build]
automatically_update_urls_on_dev = true
include_config_on_deploy = true

[webhooks]
api_version = "2024-07"

  [[webhooks.subscriptions]]
  uri = "https://dfb8-112-134-152-11.ngrok-free.app/webhooks/customers/data_request"
  compliance_topics = [ "customers/data_request" ]

  [[webhooks.subscriptions]]
  uri = "https://dfb8-112-134-152-11.ngrok-free.app/webhooks/customers/redact"
  compliance_topics = [ "customers/redact" ]

  [[webhooks.subscriptions]]
  uri = "https://dfb8-112-134-152-11.ngrok-free.app/webhooks/shop/redact"
  compliance_topics = [ "shop/redact" ]

  [[webhooks.subscriptions]]
  topics = [ "customers/create", "orders/create" ]
  uri = "arn:aws:events:us-west-2::event-source/aws.partner/shopify.com/195593830401/shopify-so-loyalty"

  [[webhooks.subscriptions]]
  topics = [ "app/uninstalled" ]
  uri = "https://dfb8-112-134-152-11.ngrok-free.app/webhooks/app/uninstalled"

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "read_customers,read_discounts,read_orders,write_customers,write_discounts,write_products"

[auth]
redirect_urls = [
  "https://comfort-symposium-sun-geneva.trycloudflare.com/auth/callback",
  "https://comfort-symposium-sun-geneva.trycloudflare.com/auth/shopify/callback",
  "https://comfort-symposium-sun-geneva.trycloudflare.com/api/auth/callback"
]

[app_proxy]
url = "https://comfort-symposium-sun-geneva.trycloudflare.com/proxy"
subpath = "members"
prefix = "apps"

[pos]
embedded = true
