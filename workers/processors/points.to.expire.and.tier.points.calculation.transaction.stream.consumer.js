const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../../.env') });
const mongoose = require('mongoose');
const { last } = require('lodash');
const moment = require('moment');
const KafkaConnector = require('../../lib/db/connectors/KafkaConnector');
const MongooseConnector = require('../../lib/db/connectors/MongooseConnector');
const MembersDAO = require('../../lib/db/dao/MembersDAO');
const TiersDAO = require('../../lib/db/dao/TiersDAO');
const { STATUS } = require('../../lib/db/models/enums/consumer.metadata.status.enum');
const Utils = require('../../lib/Utils');
const config = require('../../lib/config');
const secretConfig = require('../../config');
const logger = require('../../lib/logger');
const InsightCalculationsMemberUpdateJobProcessor = require('../../workers/processors/insight.calculations.member.update.job.processor');
const getBirthdayPointRule = require('../../workers/processors/birthday.point.rule.processor').getBirthdayPointRule;
const { birthdayPointRulesProcessingQueue } = require('../../workers/queues/queues');
const RedisConnector = require('../../lib/db/connectors/RedisConnector');
const { delAsync } = RedisConnector.getCommands();
const CacheHandler = require('../../lib/handlers/CacheHandler');
const MembersAnalyticsDAO = require('../../lib/db/dao/analytics/MembersAnalyticsDAO');
const { SYSTEM_EVENTS } = require('../../lib/constants/Constants');
const Shoutout = require('../../lib/services/Shoutout');

const log = logger(config.logger);

const updateMemberPointsToExpire = async ({ memberId, organizationId, regionId, redeemablePoints, subType, type }) => {
    log.info(`retrieving member...`);
    const { pointsToExpire } = await MembersDAO.getMemberById(memberId, organizationId);
    log.info(`member points to expire: ${pointsToExpire}`);

    const calculatedPointsToExpire = await Utils.calculatePointsToExpire(pointsToExpire, redeemablePoints, subType, organizationId, regionId, type);
    log.info(`calculated member points to expire: ${calculatedPointsToExpire}`);

    log.info(`updating member...`);
    await MembersDAO.updateMember({
        pointsToExpire: calculatedPointsToExpire,
    }, memberId, organizationId, null);
};

const calculateTierPoints = async ({ organizationId, subType, redeemablePoints }) => {
    log.info(`retrieving organization configuration...`);
    const { configuration: { tierConfiguration: { tierCalculationSubTransactionTypeIds } } } = await Utils.getOrganizationData(organizationId);
    log.info(`tier calculation sub transaction types: ${JSON.stringify(tierCalculationSubTransactionTypeIds)}`);

    if (tierCalculationSubTransactionTypeIds && Array.isArray(tierCalculationSubTransactionTypeIds) && tierCalculationSubTransactionTypeIds.includes(subType)) {
        log.info(`tier points calculating...`);
        return redeemablePoints;
    }

    log.info(`no tier points returning 0`);
    return 0;
};

const checkBirthdayPointRuleAvailability = async (organizationId, regionId) => {
    const birthdayPointRuleConfig = await getBirthdayPointRule(organizationId, regionId);
    return !!birthdayPointRuleConfig.enabled;
};

const addBirthdayBonusPointsCalculationJob = async (organizationId, regionId, memberId, baseAmount) => {
    birthdayPointRulesProcessingQueue.add({
        organizationId,
        regionId,
        memberId,
        baseAmount
    },{
        jobId: mongoose.Types.ObjectId().toString()
    });
};

const updateMemberTier = async (tierPoints, transaction = {}, member = {}) => {
    try {
        const tiersMapKey = `tiersMap:${transaction.organizationId}:${transaction.regionId}`;
        let tiers = [];

        // * Fetch tiers from cache.
        try {
            const cachedTiersMap = await CacheHandler.get(tiersMapKey);
            tiers = cachedTiersMap ? JSON.parse(cachedTiersMap) : [];
        } catch (err) {
            log.error('failed to fetch tiers from cache', err);
        }

        // * Fetch tiers from the db and save to cache.
        if (tiers?.length === 0) {
            log.info(
                `loading tiers for organization '${transaction.organizationId}' and region '${transaction.regionId}'...`
            );

            const { items } = await TiersDAO.getTiers(transaction.organizationId, {
                regionId: transaction.regionId,
                sort: { points: 'desc', orderCount: 'desc' }
            });
            tiers = items ?? [];

            log.info(
                `completed loading tiers for organization '${transaction.organizationId}' and region '${transaction.regionId}'.`
            );

            try {
                log.info(
                    `saving tiers for organization '${transaction.organizationId}' and region '${transaction.regionId}' into cache...`
                );
                await CacheHandler.set(tiersMapKey, JSON.stringify(tiers));
                log.info(
                    `saved tiers for organization '${transaction.organizationId}' and region '${transaction.regionId}' to cache.`
                );
            } catch (err) {
                log.error(
                    `failed to save tiers for organization '${transaction.organizationId}' and region '${transaction.regionId}' to cache.`,
                    err
                );
            }
        } else {
            log.info(
                `tiers for organization '${transaction.organizationId}' and region '${transaction.regionId}' fetched from Node cache.`
            );
            log.info('cached tier count', tiers?.length);
        }

        log.info('updating member tier data...');
        log.info('member current tier points:', member?.tierPoints || '0');
        tierPoints += member?.tierPoints || 0; // * Add the newly calculated tier points to the current tier points of the member.

        let tierId = last(tiers)?._id?.toString(), tierName = last(tiers)?.name;

        for (const tier of tiers) {
            if (tierPoints > tier.points && ((member?.purchasesCount || 0) > tier.orderCount || 0)) {
                tierId = tier._id.toString();
                tierName = tier?.name;
                break;
            }
        }
        log.debug('tier points:', tierPoints);
        log.debug('tier id:', tierId, 'tier name:', tierName);

        if (tierId !== member?.tier?.tierId?.toString()) {
            const previousTier = tiers.find(tier => tier._id.toString() === member?.tier?.tierId?.toString());
            const newTier = tiers.find(tier => tier._id.toString() === tierId);
            const activity = {
                memberId: member._id.toString(),
                organizationId: transaction.organizationId,
                regionId: transaction.regionId,
                createdOn: new Date(),
                activityName: SYSTEM_EVENTS.TIER_UPGRADE,
                activityData: {
                    previousTier: previousTier?.name,
                    previousTierPointMargin: previousTier?.points,
                    newTierId: tierId,
                    newTier: newTier.name,
                    newTierPointMargin: newTier.points,
                    tierPoints,
                },
            };
            await Shoutout.produceActivityToTopic(activity);
        }

        await MembersDAO.updateMember(
            {
                tierPoints: tierPoints,
                'tier.tierId': tierId,
                'tier.tierJobId': null, // * Set to 'null' because no tier job is created for instant tier update.
                'tier.lastUpdatedOn': new Date()
            },
            transaction.memberId,
            transaction.organizationId,
            null
        );

        // * Invalidate member's cache.
        try {
            await delAsync(`members:${transaction.organizationId}:${transaction.memberId}`);
        } catch (err) {
            log.error('failed to invalidate member cache', err);
        }

        log.info('completed member tier update.');
    } catch (err) {
        log.error('failed to update member tier', err);
    }
};

class PointsToExpireAndTierPointsCalculationTransactionStreamConsumer {
    static async startProcess() {
        try {
            const consumerGroupId = 'pointsToExpireAndTierCalculationStreamConsumer';

            const consumer = KafkaConnector.getConsumer(consumerGroupId);
            await consumer.connect();
            log.info(`consumer with groupId: '${consumerGroupId}' connected`);

            await consumer.subscribe({
                topic: secretConfig.TRANSACTIONS_TOPIC, fromBeginning: false,
            });
            log.info(`subscribed topic: '${secretConfig.TRANSACTIONS_TOPIC}'`);

            log.info(`initializing mongo secondary connection...`);
            const conn = await MongooseConnector.getSecondaryConnection();
            const TransactionModel = conn.model('Transaction', new mongoose.Schema({}, { strict: false }), 'transactions');

            await consumer.run({
                eachMessage: async ({ topic, partition, message }) => {
                    try {
                        log.info(`Received data from partition: ${partition} and message: ${message.value.toString()} into ${consumerGroupId}`);
                        const transaction = JSON.parse(message.value.toString());

                        const updateResult = await TransactionModel.findOneAndUpdate({
                            _id: mongoose.Types.ObjectId(transaction._id),
                            organizationId: mongoose.Types.ObjectId(transaction.organizationId),
                            'consumerMetadata.tierAndPointsToExpireProcessor': STATUS.PENDING,
                        }, {
                            $set: {
                                'consumerMetadata.tierAndPointsToExpireProcessor': STATUS.PROCESSING,
                            }, $currentDate: { updatedOn: true },
                        }, {
                            upsert: false, new: true, returnOriginal: false,
                        });

                        if (updateResult) {
                            const updateObj = {
                                'consumerMetadata.tierAndPointsToExpireProcessor': STATUS.COMPLETED,
                            };

                            log.info(`checking signed redeemable points...`);
                            if (transaction.signedRedeemablePoints < 0) {
                                log.info(`signed redeemable points amount is less than 0. calculating member points to expire...`);
                                await updateMemberPointsToExpire(transaction);
                                log.info(`member updated!`);
                            } else {
                                updateObj.tierPoints = await calculateTierPoints(transaction);
                                log.info(`updating transaction...`);

                                log.info(`retrieving member...`);
                                const member = await Utils.getMemberFromCache(transaction.organizationId, transaction.memberId);
                                if (member.birthDate) {
                                    log.info(`birthData available for member. checking the birthday...`);
                                    const monthOfTheYear = moment().month();
                                    const dayOfTheMonth = moment().date();
                                    const memberBirthMonth = moment(member.birthDate).month();
                                    const memberBirthDay = moment(member.birthDate).date();
                                    log.info(`monthOfTheYear: ${monthOfTheYear}, dayOfTheMonth: ${dayOfTheMonth}`);
                                    if (memberBirthMonth === monthOfTheYear &&  memberBirthDay === dayOfTheMonth) {
                                        log.info(`checking birthday point rule availability...`);
                                        const birthdayPointRuleAvailable = await checkBirthdayPointRuleAvailability(transaction.organizationId, transaction.regionId);
                                        if (birthdayPointRuleAvailable) {
                                            log.info(`birthday point rule available. creating job...`);
                                            await addBirthdayBonusPointsCalculationJob(transaction.organizationId, transaction.regionId, transaction.memberId, transaction.signedRedeemablePoints);
                                        }
                                    }
                                }

                                // * Update member's tier and tier points.
                                if (updateObj.tierPoints > 0) {
                                    log.info('tier points for member', updateObj.tierPoints);
                                    log.info('fetching member via SECONDARY connection...');
                                    const memberForTierUpdates = await MembersAnalyticsDAO.getMemberById(
                                        transaction.organizationId,
                                        transaction.memberId
                                    );
                                    log.debug(
                                        'fetched member tier points:',
                                        memberForTierUpdates?.tierPoints ?? '~not found'
                                    );
                                    log.debug(
                                        'fetched member purchases count:',
                                        memberForTierUpdates?.purchasesCount ?? '~not found'
                                    );
                                    await updateMemberTier(updateObj.tierPoints, transaction, memberForTierUpdates);
                                }
                            }

                            const updateResult = await TransactionModel.findOneAndUpdate({
                                _id: mongoose.Types.ObjectId(transaction._id),
                                organizationId: mongoose.Types.ObjectId(transaction.organizationId),
                                'consumerMetadata.tierAndPointsToExpireProcessor': STATUS.PROCESSING,
                            }, {
                                $set: updateObj, $currentDate: { updatedOn: true },
                            }, {
                                upsert: false, new: true, returnOriginal: false,
                            });

                            if (updateResult) {
                                log.info(`transaction updated!`);
                            } else {
                                log.error(`couldn't update the transaction`);
                            }

                        } else {
                            log.warn(`couldn't process the transaction. possible duplicate transaction from the transactions topic`);
                        }

                        InsightCalculationsMemberUpdateJobProcessor.addJob(transaction.organizationId, transaction.memberId, transaction.regionId);

                    } catch (e) {
                        log.error(e);
                        log.error(`error while processing the transaction: ${message.value.toString()}`);
                        const transaction = JSON.parse(message.value.toString());
                        await TransactionModel.findOneAndUpdate({
                            _id: mongoose.Types.ObjectId(transaction._id),
                            organizationId: mongoose.Types.ObjectId(transaction.organizationId)
                        }, {
                            $set: {
                                'consumerMetadata.tierAndPointsToExpireProcessor': STATUS.FAILED,
                            }, $currentDate: { updatedOn: true },
                        }, {
                            upsert: false, new: true, returnOriginal: false,
                        });
                    }
                },
            });

        } catch (e) {
            log.error(e);
            return Promise.reject(e);
        }
    }

}

module.exports = PointsToExpireAndTierPointsCalculationTransactionStreamConsumer;
