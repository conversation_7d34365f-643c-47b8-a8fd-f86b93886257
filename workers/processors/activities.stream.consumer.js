const path = require('path');
require('dotenv').config({path: path.resolve(__dirname, '../../.env')});
const KafkaConnector = require('../../lib/db/connectors/KafkaConnector');
const config = require('../../lib/config');
const secretConfig = require('../../config');
const logger = require('../../lib/logger');
const { SYSTEM_EVENTS: { TIER_UPGRADE } } = require('../../lib/constants/Constants');
const { tierBonusPointsAwardingJobQueue } = require('../queues/queues');
const log = logger(config.logger);

class ActivitiesStreamConsumer {

    static async startProcess() {
        try {
            const consumer = KafkaConnector.getConsumer('loyalty-activity-consumer');
            await consumer.connect();
            log.info(`consumer with groupId: loyalty-activity-consumer connected`);

            await consumer.subscribe({
                topic: secretConfig.ACTIVITIES_TOPIC,
                fromBeginning: true
            });
            log.info(`subscribed topic: '${secretConfig.ACTIVITIES_TOPIC}'`);

            const ctx = this;

            log.info(`starting activity consumer for loyalty service...`);
            await consumer.run({
                eachMessage: async ({topic, partition, message}) => {
                    log.debug(`Received data from partition: ${partition} and message: ${message.value.toString()} into ${secretConfig.KAFKA_CLIENT_ID}`);
                    const activity = JSON.parse(message.value.toString());
                    await ctx.filterEvents(activity);
                }
            });
        } catch (e) {
            log.error(e);
            return Promise.reject(e);
        }
    }

    static async filterEvents(activity) {
        if (activity.activityName === TIER_UPGRADE) {
            await tierBonusPointsAwardingJobQueue.add({
                organizationId: activity.organizationId,
                regionId: activity.regionId,
                memberId: activity.memberId,
                tierId: activity.activityData?.newTierId,
            });
        }
    }

}

module.exports = ActivitiesStreamConsumer;