'use client'

import { useRewards } from '@/stores/rewardStore'
import { format } from 'date-fns'
import { Loader } from '../../common'
import { useQuery } from '@tanstack/react-query'
import rewardsService from '@/lib/services/rewardsService'
import { useAuthStore } from '@/stores/authStore'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import { ASSETS } from '@/config'

export default function RewardDetails() {
  const { rewards, isLoading: rewardsLoading, error: rewardsError } = useRewards()
  const { data, isLoading: redeemedLoading, error: redeemedError } = useQuery({
    queryKey: ['redemptionLogs'],
    queryFn: () => rewardsService.getRedemptionLogs({
      limit: 300,
      skip: 0
    }),
    staleTime: 0,
    gcTime: 0,
    refetchOnMount: 'always',
    refetchOnWindowFocus: true,
    refetchOnReconnect: true,
    retry: 3,
    retryDelay: 1000,
    enabled: useAuthStore.getState().isAuthenticated
  })

  const redeemedRewards = (data?.items || [])
    .sort((a: { createdOn: string | Date }, b: { createdOn: string | Date }) => new Date(b.createdOn).getTime() - new Date(a.createdOn).getTime())
    .slice(0, 5)

  if (rewardsError || redeemedError) return <div className="text-red-400">Failed to fetch rewards</div>

  return (
    <div className="space-y-4 sm:space-y-6 font-perpetua">
      {/* Available Rewards Section */}
      <Card className="bg-foreground rounded-lg border border-secondary/30">
        <CardHeader className="flex flex-col sm:flex-row justify-between items-start sm:items-center px-4 sm:px-6 py-4 border-b border-secondary/30 gap-2 sm:gap-0">
          <CardTitle className="text-primary font-bold">All Rewards</CardTitle>
          <a
            href='/portal/rewards'
            className={`text-sm font-medium font-glacial ${rewardsLoading ? 'pointer-events-none text-primary/50' : 'text-primary hover:text-primary/80  cursor-pointer z-10'}`}
            aria-disabled={rewardsLoading}
            tabIndex={rewardsLoading ? -1 : undefined}
          >
            View All Rewards
          </a>
        </CardHeader>
        <CardContent className="px-4 sm:px-6 pb-4">
          {
            rewardsLoading ? <div className="h-full w-full"><Loader loaderText="Loading rewards" /></div> :
              rewards.length !== 0 && rewards.map((reward: { _id: string; imageUrls: (string | undefined)[]; name: string | undefined; shortDescription: string | undefined; pointsStatic: number | undefined }) => (
                <Card key={reward._id} className="bg-background mt-3 rounded-lg p-3 sm:p-4 mb-3 last:mb-0 border border-primary ">
                  <CardContent className="flex items-center justify-between p-3 sm:p-4">
                    <div className="flex items-center space-x-3 sm:space-x-4">
                      <div className="w-10 h-10 sm:w-12 sm:h-12">
                        <img
                          className="w-full h-full object-cover rounded-lg"
                          src={reward.imageUrls[0]?.startsWith("https") ? reward.imageUrls[0] : ASSETS.REWARD_IMAGE}
                          alt={reward.name}
                          onError={(e: React.SyntheticEvent<HTMLImageElement>) => {
                            const target = e.currentTarget;
                            target.src = ASSETS.REWARD_IMAGE;
                          }}
                        />
                      </div>
                      <div>
                        <p className="font-medium font-perpetua text-primary text-sm sm:text-base">{reward.name}</p>
                        <p className="text-xs sm:text-sm text-primary font-glacial">{reward.shortDescription}</p>
                      </div>
                    </div>
                    <p className="font-semibold text-right font-glacial text-primary text-sm sm:text-base ml-2">
                      {reward.pointsStatic} points
                    </p>
                  </CardContent>
                </Card>
              ))}
        </CardContent>
        {!rewardsLoading && rewards.length === 0 && (
          <p className="text-center py-4 text-primary/60 font-glacial">Rewards coming soon...</p>
        )}
      </Card>

      {/* Redeemed Rewards Section */}
      <Card className="bg-foreground rounded-lg border border-secondary/30">
        <CardHeader className="flex flex-col sm:flex-row justify-between items-start sm:items-center px-4 sm:px-6 py-4 border-b border-secondary/30 gap-2 sm:gap-0">
          <CardTitle className="text-lg font-semibold font-perpetua text-primary">Redeemed Rewards</CardTitle>
        </CardHeader>
        <CardContent className="px-4 sm:px-6 pb-4">
          {
            redeemedLoading ? <div className="h-full w-full"><Loader loaderText="Loading your redemptions" /></div> :
              redeemedRewards.map((redemption: { distributionJobId: string; reward: { imageUrls: (string | undefined)[]; name: string | undefined }; pickupLocation: { locationName: string | undefined }; processingStatus: string; createdOn: string | Date }) => (
                <Card key={redemption.distributionJobId} className="bg-background mt-3 rounded-lg p-3 sm:p-4 mb-3 last:mb-0 border border-primary">
                  <CardContent className="flex items-center justify-between p-3 sm:p-4">
                    <div className="flex items-center space-x-3 sm:space-x-4">
                      <div className="w-10 h-10 sm:w-12 sm:h-12">
                        <img
                          className="w-full h-full object-cover rounded-lg"
                          src={redemption.reward.imageUrls[0]?.startsWith("https") ? redemption.reward.imageUrls[0] : ASSETS.REWARD_IMAGE}
                          alt={redemption.reward.name}
                          onError={(e: React.SyntheticEvent<HTMLImageElement>) => {
                            const target = e.currentTarget;
                            target.src = ASSETS.REWARD_IMAGE;
                          }}
                        />
                      </div>
                      <div>
                        <p className="font-medium font-perpetua text-primary text-sm sm:text-base">{redemption.reward.name}</p>
                        <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-2">
                          <p className="text-xs sm:text-sm text-primary font-glacial">
                            {redemption.pickupLocation?.locationName}
                          </p>
                          <span className="inline-block w-fit px-2 py-1 text-xs rounded-full bg-secondary/20 text-primary font-glacial mt-1 sm:mt-0">
                            {redemption.processingStatus}
                          </span>
                        </div>
                        <p className="text-xs text-muted-dark font-glacial mt-1">
                          {format(new Date(redemption.createdOn), 'MMM dd, yyyy h:mm a')}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
          {!redeemedLoading && redeemedRewards.length === 0 && (
            <p className="text-center py-4 text-primary/60 font-glacial">No redeemed rewards</p>
          )}
        </CardContent>
      </Card>
    </div>
  )
} 