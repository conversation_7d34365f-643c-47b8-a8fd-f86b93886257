import React from "react";
import { CopyToClipboard } from "react-copy-to-clipboard";
import { toast } from "react-toastify";
import PropTypes from "prop-types";
import {
    IcIcon,
    OverlayTrigger,
    Tooltip,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { faCopy } from "FaICIconMap";

const CopyComponent = ({ dataToCopy, copyToast }) => (
    <CopyToClipboard
        onCopy={() => {
            toast.info(copyToast || "Copied to clipboard.");
        }}
        text={dataToCopy}
    >
        <div className="w-100 d-flex justify-content-between align-items-center cursor-copy">
            <div className="text-left">{dataToCopy}</div>
            <IcIcon className="ml-3" icon={faCopy} size="2x" />
        </div>
    </CopyToClipboard>
);

const CopyItemToClipboard = ({
    dataToCopy,
    copyTooltip,
    copyToast,
    noToolTip,
}) => {
    return noToolTip ? (
        <CopyComponent dataToCopy={dataToCopy} copyToast={copyToast} />
    ) : (
        <OverlayTrigger
            placement="bottom"
            overlay={<Tooltip id="tooltip-copy-card">{copyTooltip}</Tooltip>}
        >
            <CopyToClipboard
                onCopy={() => {
                    toast.info(copyToast || "Copied to clipboard.");
                }}
                text={dataToCopy}
            >
                <div className="w-100 d-flex justify-content-between align-items-center cursor-copy">
                    <div className="text-left">{dataToCopy}</div>
                    <IcIcon className="ml-3" icon={faCopy} size="2x" />
                </div>
            </CopyToClipboard>
        </OverlayTrigger>
    );
};

CopyItemToClipboard.defaultProps = {
    dataToCopy: "",
    copyTooltip: "",
    copyToast: "",
    noToolTip: false,
};

CopyItemToClipboard.propTypes = {
    dataToCopy: PropTypes.string,
    copyTooltip: PropTypes.string,
    copyToast: PropTypes.string,
    noToolTip: PropTypes.bool,
};

export default CopyItemToClipboard;
