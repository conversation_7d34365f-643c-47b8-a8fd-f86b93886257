import React, { useCallback, useEffect, useState } from "react";
import { toast } from "react-toastify";
import PropTypes from "prop-types";
import {
    Button,
    IcIcon,
    Tab,
    Tabs,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { faAngleLeftB, faAward, faSync } from "FaICIconMap";
import {
    RewardGenerationJobGroups,
    RewardGenerationJobStatus,
    RewardRedemptionProcessStatus,
    RedemptionStatus,
} from "Data";
import BaseLayout from "Layout/BaseLayout";
import RewardLogisticsTable from "../rewardLogisticsTable/RewardLogisticsTable";
import ChangeProcess from "../shared/changeProcess/ChangeProcess";
import RewardLogisticDetails from "./details/RewardLogisticDetails";

import "./RewardLogisticsProfile.scss";

const defaultLimit = 10,
    defaultSkip = 1;

const RewardLogisticsProfile = ({
    batchId,
    selectedBatch,
    isLoading,
    loadRewardLogistics,
    loadRedemptionLogs,
    selectedTab,
    group,
    changeBatchProcess,
    batchProcessDetails,
    onBatchProcessStatusChange,
    onHideBatchProcessStatusChange,
    isExporting,
    onExportBatch,
    viewMemberDetails,
    memberId,
    setMemberId,
    onShowMemberDetails,
    onHideMemberDetails,
    onNavigatingBack,
    isAllowedUpdateBatchProcess,
}) => {
    const [limit, setLimit] = useState(defaultLimit);
    const [skip, setSkip] = useState(defaultSkip);
    const [redemptionLogsByBatch, setRedemptionLogsByBatch] = useState([]);
    const [redemptionLogsByBatchCount, setRedemptionLogsByBatchCount] =
        useState(0);
    const [isReloadingRedemptionsByBatch, setIsReloadingRedemptionsByBatch] =
        useState(false);
    const [changeProcess, setChangeProcess] = useState(false);
    const [processDetails, setProcessDetails] = useState({});
    const [refundPointsModal, setRefundPointsModal] = useState(false);
    const [failedRedemption, setFailedRedemption] = useState({});

    const loadRedemptionLogsByBatch = useCallback(
        async ({ limit, skip }) => {
            try {
                if (!batchId) throw new Error("'batchId' not found");

                const redemptionLogsBatchResponse = await loadRedemptionLogs(
                    { limit, skip },
                    null,
                    batchId
                );
                setRedemptionLogsByBatch(redemptionLogsBatchResponse?.list);
                setRedemptionLogsByBatchCount(
                    redemptionLogsBatchResponse?.total
                );
            } catch (error) {
                console.error(error);
                toast.error(
                    <div>
                        Failed to load redemption logs for batch!
                        <br />
                        {error.message
                            ? `Error: ${error.message}`
                            : "Please try again later."}
                    </div>
                );
            }
        },
        [
            batchId,
            loadRedemptionLogs,
            setRedemptionLogsByBatch,
            setRedemptionLogsByBatchCount,
        ]
    );

    const reloadRedemptionLogs = useCallback(async () => {
        setIsReloadingRedemptionsByBatch(true);
        await loadRedemptionLogsByBatch({ limit, skip: defaultSkip });
        setIsReloadingRedemptionsByBatch(false);
        setSkip(defaultSkip);
    }, [
        limit,
        setIsReloadingRedemptionsByBatch,
        loadRedemptionLogsByBatch,
        setSkip,
    ]);

    const onProcessStatusChange = useCallback(
        (e) => {
            let processDetailsObj = {
                id: e.currentTarget.id,
                group: RewardGenerationJobGroups.INDIVIDUAL,
            };

            switch (e.currentTarget.name) {
                case RewardRedemptionProcessStatus.DISPATCH:
                    processDetailsObj = {
                        ...processDetailsObj,
                        action: RewardRedemptionProcessStatus.DISPATCH,
                        status: RedemptionStatus.READY,
                        processingStatus: RewardGenerationJobStatus.DISPATCHED,
                    };
                    break;
                case RewardRedemptionProcessStatus.COMPLETE:
                    processDetailsObj = {
                        ...processDetailsObj,
                        action: RewardRedemptionProcessStatus.COMPLETE,
                        status: RedemptionStatus.CLAIMED,
                        processingStatus: RewardGenerationJobStatus.COMPLETED,
                    };
                    break;
                case RewardRedemptionProcessStatus.FAIL:
                    processDetailsObj = {
                        ...processDetailsObj,
                        action: RewardRedemptionProcessStatus.FAIL,
                        status: RedemptionStatus.CANCELLED,
                        processingStatus: RewardGenerationJobStatus.FAILED,
                    };
                    break;
                default:
                    return null;
            }
            setProcessDetails(processDetailsObj);
            setChangeProcess(true);
        },
        [setProcessDetails, setChangeProcess]
    );

    const onRefundPoints = useCallback(
        (e) => {
            const currentId = e.currentTarget.id;
            e.stopPropagation();
            const failedItem = redemptionLogsByBatch.find(
                (redemption) => redemption._id === currentId
            );
            setFailedRedemption(failedItem);
            setRefundPointsModal(true);
        },
        [redemptionLogsByBatch, setFailedRedemption, setRefundPointsModal]
    );

    const onHideProcessStatusChange = useCallback(
        (e, data) => {
            if (data) {
                loadRedemptionLogsByBatch({ limit, skip });
            }
            setProcessDetails({});
            setChangeProcess(false);
        },
        [
            limit,
            skip,
            setProcessDetails,
            setChangeProcess,
            loadRedemptionLogsByBatch,
        ]
    );

    const onHideRefundPoints = useCallback(
        (e, data) => {
            if (data) {
                loadRedemptionLogsByBatch({ limit, skip });
            }
            setMemberId("");
            setFailedRedemption({});
            setRefundPointsModal(false);
        },
        [
            limit,
            skip,
            setMemberId,
            loadRedemptionLogsByBatch,
            setFailedRedemption,
            setRefundPointsModal,
        ]
    );

    useEffect(() => {
        loadRedemptionLogsByBatch({ limit, skip: defaultSkip });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [batchId]);

    useEffect(() => {
        if (
            redemptionLogsByBatch.length !== 0 &&
            redemptionLogsByBatch.every(
                (rLBB) =>
                    rLBB?.processingStatus ===
                        RewardGenerationJobStatus.FAILED &&
                    rLBB?.status === RedemptionStatus.CANCELLED
            )
        ) {
            toast.info(
                <div>
                    {"All redemptions "}
                    {selectedBatch?.batch_id
                        ? `of "Batch ${selectedBatch.batch_id}"`
                        : ""}
                    {" were "}
                    {RewardGenerationJobStatus.FAILED}
                    {" and "}
                    {RedemptionStatus.CANCELLED},
                    {" so the whole batch was marked as "}
                    {RewardGenerationJobStatus.FAILED}.
                </div>
            );
            onNavigatingBack(true);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [redemptionLogsByBatch]);

    return (
        <div className="reward-logistics-profile">
            <BaseLayout
                bottom={
                    <>
                        <div>
                            <Button
                                className="btn shadow-none"
                                variant="link"
                                size="lg"
                                disabled={
                                    isLoading || isReloadingRedemptionsByBatch
                                }
                                onClick={onNavigatingBack}
                            >
                                <div className="d-flex align-items-center">
                                    <IcIcon
                                        icon={faAngleLeftB}
                                        size="lg"
                                        className="mr-1"
                                    />
                                    Back
                                </div>
                            </Button>
                        </div>

                        <RewardLogisticDetails
                            isLoading={
                                isLoading || isReloadingRedemptionsByBatch
                            }
                            selectedTab={selectedTab}
                            selectedBatch={selectedBatch}
                            onBatchProcessStatusChange={
                                onBatchProcessStatusChange
                            }
                            isExporting={isExporting}
                            onExportBatch={onExportBatch}
                            isAllowedUpdateBatchProcess={
                                isAllowedUpdateBatchProcess
                            }
                        />

                        <Tabs
                            defaultActiveKey="rewards-list"
                            transition={false}
                            id="noanim-tab-example"
                            activeKey="rewards-list"
                            className="mt-3 border-solid-bottom"
                        >
                            <Tab
                                eventKey="rewards-list"
                                title={
                                    <div className="d-flex align-items-center">
                                        <IcIcon
                                            className="mr-2"
                                            size="lg"
                                            icon={faAward}
                                        />
                                        Rewards List
                                    </div>
                                }
                            >
                                <div className="mt-5">
                                    <div>
                                        {isReloadingRedemptionsByBatch ? (
                                            <div
                                                className="ml-3 pt-2 text-primary"
                                                style={{ fontSize: "0.9rem" }}
                                            >
                                                Reloading...
                                            </div>
                                        ) : (
                                            <Button
                                                variant="link"
                                                size="sm"
                                                disabled={
                                                    isLoading ||
                                                    isReloadingRedemptionsByBatch
                                                }
                                                onClick={reloadRedemptionLogs}
                                            >
                                                {!isReloadingRedemptionsByBatch && (
                                                    <div className="d-flex align-items-center">
                                                        <IcIcon
                                                            size="md"
                                                            className="mr-2"
                                                            icon={faSync}
                                                        />
                                                        Reload Rewards List
                                                    </div>
                                                )}
                                            </Button>
                                        )}
                                    </div>
                                    <div className="w-100">
                                        <RewardLogisticsTable
                                            redemptionLogsList={
                                                redemptionLogsByBatch
                                            }
                                            loadRedemptionLogs={
                                                loadRedemptionLogsByBatch
                                            }
                                            totalCount={
                                                redemptionLogsByBatchCount
                                            }
                                            isLoading={isLoading}
                                            rewardStatus={selectedTab}
                                            group={group}
                                            limit={limit}
                                            skip={skip}
                                            setLimit={setLimit}
                                            setSkip={setSkip}
                                            changeProcess={changeProcess}
                                            processDetails={processDetails}
                                            onProcessStatusChange={
                                                onProcessStatusChange
                                            }
                                            onHideProcessStatusChange={
                                                onHideProcessStatusChange
                                            }
                                            refundPointsModal={
                                                refundPointsModal
                                            }
                                            failedRedemption={failedRedemption}
                                            onRefundPoints={onRefundPoints}
                                            onHideRefundPoints={
                                                onHideRefundPoints
                                            }
                                            viewMemberDetails={
                                                viewMemberDetails
                                            }
                                            memberId={memberId}
                                            onShowMemberDetails={
                                                onShowMemberDetails
                                            }
                                            onHideMemberDetails={
                                                onHideMemberDetails
                                            }
                                            isAllowedUpdateBatchProcess={
                                                isAllowedUpdateBatchProcess
                                            }
                                        />
                                    </div>
                                </div>
                            </Tab>
                        </Tabs>
                    </>
                }
            />
            {batchProcessDetails &&
                typeof batchProcessDetails === "object" &&
                Object.keys(batchProcessDetails).length !== 0 &&
                changeBatchProcess && (
                    <ChangeProcess
                        show={changeBatchProcess}
                        limit={limit}
                        skip={skip}
                        processDetails={batchProcessDetails}
                        isNavigateBack
                        onHide={onHideBatchProcessStatusChange}
                        loadLogistics={loadRewardLogistics}
                        navigateBack={onNavigatingBack}
                    />
                )}
        </div>
    );
};

RewardLogisticsProfile.defaultProps = {
    batchId: "",
    selectedBatch: {},
    isLoading: false,
    memberId: "",
    isAllowedUpdateBatchProcess: false,
    group: "",
    selectedTab: "",
    batchProcessDetails: {},
    viewMemberDetails: false,
    changeBatchProcess: false,
    isExporting: false,
    setMemberId: () => {},
    loadRewardLogistics: () => {},
    loadRedemptionLogs: () => {},
    onBatchProcessStatusChange: () => {},
    onHideBatchProcessStatusChange: () => {},
    onExportBatch: () => {},
    onShowMemberDetails: () => {},
    onHideMemberDetails: () => {},
    onNavigatingBack: () => {},
};

RewardLogisticsProfile.propTypes = {
    batchId: PropTypes.string,
    selectedBatch: PropTypes.object,
    isLoading: PropTypes.bool,
    memberId: PropTypes.string,
    isAllowedUpdateBatchProcess: PropTypes.bool,
    group: PropTypes.string,
    selectedTab: PropTypes.string,
    batchProcessDetails: PropTypes.object,
    viewMemberDetails: PropTypes.bool,
    changeBatchProcess: PropTypes.bool,
    isExporting: PropTypes.bool,
    setMemberId: PropTypes.func,
    loadRewardLogistics: PropTypes.func,
    loadRedemptionLogs: PropTypes.func,
    onBatchProcessStatusChange: PropTypes.func,
    onHideBatchProcessStatusChange: PropTypes.func,
    onExportBatch: PropTypes.func,
    onShowMemberDetails: PropTypes.func,
    onHideMemberDetails: PropTypes.func,
    onNavigatingBack: PropTypes.func,
};

export default RewardLogisticsProfile;
