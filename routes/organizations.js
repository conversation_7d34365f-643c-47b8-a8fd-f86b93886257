'use strict';
const express = require('express');
const router = express.Router();
const OrganizationHandler = require('../lib/handlers/OrganizationHandler');

/**
 * @openapi
 *
 * definitions:
 *      Address:
 *          type: object
 *          properties:
 *              line1:
 *                  type: string
 *              line2:
 *                  type: string
 *              line3:
 *                  type: string
 *              city:
 *                  type: string
 *              stateOrProvince:
 *                  type: string
 *              zipOrPostcode:
 *                  type: string
 *      MemberStatusUpdateWebhookConfig:
 *          type: object
 *          properties:
 *              status:
 *                  type: boolean
 *              description:
 *                  type: string
 *      WebhookConfiguration:
 *          type: object
 *          properties:
 *              memberStatusUpdate:
 *                  $ref: '#/definitions/MemberStatusUpdateWebhookConfig'
 *      Organization:
 *          type: object
 *          properties:
 *              organizationName:
 *                  type: string
 *              organizationLogoImageUrl:
 *                  type: string
 *              organizationFavicon:
 *                  type: string
 *              organizationAppTitle:
 *                  type: string
 *              address:
 *                  $ref: '#/definitions/Address'
 *              regions:
 *                  readOnly: true
 *                  type: array
 *                  items:
 *                      $ref: '#/definitions/Region'
 *              subtransactionTypeIdMap:
 *                  type: object
 *                  readOnly: true
 *                  properties:
 *                      collectPointsBill:
 *                          type: string
 *                      collectPointsManual:
 *                          type: string
 *                      collectPointsDonation:
 *                          type: string
 *                      redeemPointsDonation:
 *                          type: string
 *                      redeemPointsReward:
 *                          type: string
 *                      adjustPointsTransferSubstract:
 *                          type: string
 *                      adjustPointsTransferAdd:
 *                          type: string
 *                      adjustPointsRegionalTransferSubtract:
 *                          type: string
 *                      adjustPointsRegionalTransferAdd:
 *                          type: string
 *                      refundPoints:
 *                          type: string
 *                      primaryAccountAdd:
 *                          type: string
 *                      secondaryAccountSubtract:
 *                          type: string
 *                      expirePoints:
 *                          type: string
 *                      cardReplacement:
 *                          type: string
 *              configuration:
 *                  type: object
 *                  properties:
 *                      baseRegionId:
 *                          type: string
 *                      baseCountryISO2Code:
 *                          type: string
 *                          readOnly: true
 *                      baseCountryCurrencyCode:
 *                          type: string
 *                          readOnly: true
 *                      cardConfiguration:
 *                          type: object
 *                          readOnly: true
 *                          properties:
 *                              loyaltyCardNumberLength:
 *                                  type: number
 *                              allowManualCardGeneration:
 *                                  type: boolean
 *                      notificationConfiguration:
 *                          type: object
 *                          properties:
 *                              emailConfiguration:
 *                                  type: object
 *                                  properties:
 *                                      fromAddress:
 *                                          type: string
 *                              smsConfiguration:
 *                                  type: object
 *                                  properties:
 *                                      phoneNumber:
 *                                          type: string
 *                      providerConfiguration:
 *                          type: object
 *                          properties:
 *                              emailProvidersList:
 *                                  type: array
 *                                  items:
 *                                      type: [string, integer]
 *                              smsProvidersList:
 *                                  type: array
 *                                  items:
 *                                      type: [string, integer]
 *                      tierConfiguration:
 *                          type: object
 *                          properties:
 *                              tierCalculationWindow:
 *                                  type: number
 *                                  minimum: 1
 *                              tierCalculationSubTransactionTypeIds:
 *                                  type: array
 *                                  items:
 *                                      type: string
 *                              jobEnabled:
 *                                  type: boolean
 *                              jobFrequency:
 *                                  type: string
 *                      reportConfiguration:
 *                          type: object
 *                          properties:
 *                              financialYearStartMonth:
 *                                  type: number
 *                                  minimum: 1
 *                                  maximum: 12
 *                      portalConfiguration:
 *                          type: object
 *                          properties:
 *                              allowSelfSignup:
 *                                  type: boolean
 *                              allowedOrigins:
 *                                  type: array
 *                                  items:
 *                                      type: string
 *                              idpMetadata:
 *                                  type: object
 *                                  properties:
 *                                      realm:
 *                                          type: string
 *                                      clientId:
 *                                          type: string
 *                                      clientSecret:
 *                                          type: string
 *                                      certUrl:
 *                                          type: string
 *                                      issuer:
 *                                          type: string
 *                                      audience:
 *                                          type: string
 *                      memberPrimaryAttribute:
 *                          type: string
 *                      webhookConfiguration:
 *                          $ref: '#/definitions/WebhookConfiguration'
 *
 */
module.exports = (authorizer) => {
    /**
     * @openapi
     *
     * /organizations:
     *      post:
     *          summary: Create an organization
     *          produces:
     *              - application/json
     *          tags:
     *              - Organizations
     *          requestBody:
     *              content:
     *                  application/json:
     *                      schema:
     *                          allOf:
     *                            - $ref: '#/definitions/Organization'
     *                            - type: object
     *                              properties:
     *                                  configuration:
     *                                      type: object
     *                                      properties:
     *                                        baseCountryISO2Code:
     *                                            type: string
     *                                            readOnly: false
     *                                        baseCountryCurrencyCode:
     *                                            type: string
     *                                            readOnly: false
     *                                        cardConfiguration:
     *                                            readOnly: false
     *                                        baseRegionId:
     *                                            readOnly: true
     *                                  defaultRegion:
     *                                      $ref: '#/definitions/Region'
     *                                  subtransactionTypeIdMap:
     *                                      readOnly: true
     *
     *          security:
     *              - Token: []
     *          responses:
     *              201:
     *                  description: Create organization is successful.
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/Organization'
     */
    router.post('/', authorizer, async (req, res, next) => {
        try {
            const payload = req.body;
            const userId = req.user['id'];
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const { result, ...rest } = await OrganizationHandler.createOrganization(
                payload,
                organizationId,
                userId,
                ability
            );
            next({
                response: { status: 201, body: result },
                ...rest
            });
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     *
     * /organizations:
     *      get:
     *          produces:
     *              - application/json
     *          tags:
     *              - Organizations
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Get the organization is successful.
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/Organization'
     */
    router.get('/', authorizer, async (req, res, next) => {
        try {
            const organizationId = req.user['organizationId'];
            // const ability = req.user['ability']; // TODO: REMOVE IF NOT NEEDED
            const result = await OrganizationHandler.getOrganization({ organizationId }, true, true, null); //todo: removed ability temporary
            res.status(200).send(result);
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     *
     * /organizations:
     *      put:
     *          produces:
     *              - application/json
     *          tags:
     *              - Organizations
     *          requestBody:
     *              content:
     *                  application/json:
     *                      schema:
     *                          $ref: '#/definitions/Organization'
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Update the organization is successful.
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/Organization'
     */
    router.put('/', authorizer, async (req, res, next) => {
        try {
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const payload = req.body;
            const { result, ...rest } = await OrganizationHandler.updateOrganization(organizationId, payload, ability);
            next({
                response: { status: 200, body: result },
                ...rest
            });
        } catch (err) {
            next(err);
        }
    });

    return router;
};
