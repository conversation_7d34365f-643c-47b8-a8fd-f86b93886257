'use strict';
const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const RewardRedemptionMetadataSchema = new Schema(
  {
    partnerRefNumber: { type: String },
    partnerRefName: { type: String },
    partnerNotes: { type: String },
    claimLocationId: { type: mongoose.Types.ObjectId },
    bundleValue: { type: Number },
  },
  { timestamps: false, _id: false },
);

module.exports = RewardRedemptionMetadataSchema;
