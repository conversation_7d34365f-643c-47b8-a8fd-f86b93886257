'use strict';

const mongoose = require('mongoose');
const ProductCatalogueSchema = require('../schemas/product.catalogue.schema');

const ProductCatalogueModelParams = ['ProductCatalogue', ProductCatalogueSchema, 'product_catalogue'];

const getProductCatalogueModel = (conn = null, customParams = []) => {
    const params = customParams.length !== 0 ? customParams : ProductCatalogueModelParams;

    if (conn) return conn.model(...params);

    return mongoose.model(...params);
};

module.exports = { getProductCatalogueModel };
