'use strict';
const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const Contact = require('./embedded/contact.schema');
const LoyaltyOptions = require('./embedded/loyaltyOptions.schema');
const { STATUS } = require('./enums/merchant.location.enums');

const MerchantLocationSchema = new Schema(
  {
    organizationId: { type: mongoose.Types.ObjectId, required: true },
    regionId: { type: mongoose.Types.ObjectId, required: true },
    merchantId: { type: mongoose.Types.ObjectId, required: true },
    locationName: { type: String, required: true },
    contact: { type: Contact, required: false },
    status: {
      type: String,
      required: true,
      enum: Object.values(STATUS),
      default: STATUS.ACTIVE,
    },
    options: { type: LoyaltyOptions, required: false },
    isPickupLocation: { type: Boolean, required: true },
    isShownInCreateTransactions: {
      type: Boolean,
      required: true,
      default: false,
    },
    code: { type: String, required: true },
    createdBy: { type: mongoose.Types.ObjectId, required: true },
    updatedBy: { type: mongoose.Types.ObjectId },
  },
  { timestamps: { createdAt: 'createdOn', updatedAt: 'updatedOn' } },
);

MerchantLocationSchema.index({ organizationId: 1, regionId: 1 });
MerchantLocationSchema.index({ organizationId: 1, regionId: 1, code: 1 },{ unique: true });
MerchantLocationSchema.index({ locationName: 'text' });

module.exports = mongoose.model(
  'MerchantLocation',
  MerchantLocationSchema,
  'merchant_locations',
);
