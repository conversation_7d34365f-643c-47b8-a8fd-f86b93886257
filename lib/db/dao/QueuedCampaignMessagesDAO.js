'use strict';
const config = require('../../config');
const logger = require('../../logger');
const log = logger(config.logger);
const QueuedCampaignMessage = require('../models/queued.campaign.job.model');
const mongoose = require('mongoose');

class QueuedCampaignMessagesDAO {
    static async createQueuedCampaignMessage(dataObj) {
        try {
            const QueuedCampaignMessageModel = new QueuedCampaignMessage(dataObj);
            const createdObj = await QueuedCampaignMessageModel.save();
            return Promise.resolve(createdObj.toObject({ getters: true }));
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async createQueuedCampaignMessageBulk(dataBulk) {
        try {
            const result = await QueuedCampaignMessage.insertMany(dataBulk, { ordered: false });
            return result.length;
        } catch (error) {
            if (error.writeErrors) {
                log.warn(`Inserted ${error.insertedDocs.length} documents`);
                log.warn(`Encountered ${error.writeErrors.length} write errors`);
                return error.insertedDocs.length;
            } else {
                log.error(`An unexpected error occurred: ${error}`);
                return Promise.reject(error);
            }
        }
    }

    static async getQueuedCampaignMessagesByCampaignId(campaignId, stream = false) {
        try {
            const filter = { campaignId: mongoose.Types.ObjectId(campaignId) };
            const query = QueuedCampaignMessage.find(filter);
            if (stream) {
                return query.cursor();
            }
            return await query;
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }
}

module.exports = QueuedCampaignMessagesDAO;
