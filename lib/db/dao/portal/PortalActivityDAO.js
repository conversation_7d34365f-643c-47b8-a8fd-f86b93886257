'use strict';
const MongoDBConnector = require('@shoutout-labs/mongodb-connector');
const config = require('./../../../config');
const secretConfig = require('./../../../../config');
const CORE_SERVICE_DB = secretConfig.MONGO_DB_CORE_SERVICE;
const ACTIVITIES_COLLECTION = 'activities';
const logger = require('./../../../logger');
const log = logger(config.logger);
const {MODULE_IDS, EVENT_TEMPLATES} = require('@shoutout-labs/constants');

class ActivityDAO {
    static async getActivities(ownerId, contactId, limit, skip) {
        try {
            const queryFilter = {
                ownerId,
                contactId,
                moduleId: MODULE_IDS.LOYALTY_MODULE,
                templateId: {$in: [EVENT_TEMPLATES.LOYALTY_POINT_COLLECTED, EVENT_TEMPLATES.LOYALTY_POINT_REDEEMED]}
            };
            const db = await MongoDBConnector.getDbConnection(CORE_SERVICE_DB, ownerId);
            const collection = db.collection(ACTIVITIES_COLLECTION);
            const cursor = collection.find(queryFilter).sort({createdOn: -1});
            const total = await cursor.count();
            if (skip) {
                cursor.skip(skip);
            }
            if (limit) {
                cursor.limit(limit);
            }
            const items = await cursor.toArray();
            return Promise.resolve({
                total,
                items: items
            });
        } catch (e) {
            log.error(e);
            return Promise.reject(e);
        }
    }
}

module.exports = ActivityDAO;
