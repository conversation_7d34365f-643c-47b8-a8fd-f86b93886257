'use strict';
const ClaimRewardValidator = require('./../validators/ClaimRewardValidator');
const CustomHttpError = require('./../CustomHttpError');
const RewardDAO = require('./../db/dao/RewardDAO');
const RewardRedemptionLogsDAO = require('./../db/dao/RewardRedemptionLogsDAO');
const RewardVoucherDAO = require('./../db/dao/RewardVoucherDAO');
const MerchantLocationsDAO = require('./../db/dao/MerchantLocationsDAO');
const { STATUS } = require('./../db/models/enums/reward.redemption.log.enums');
const { BOUNDARY } = require('./../db/models/enums/user.enums');
const { STATUS: VOUCHER_STATUS } = require('./../db/models/enums/reward.voucher.enums');
const { SUB_TYPE } = require('./../db/models/enums/reward.enums');
const Shoutout = require('../services/Shoutout');
const config = require('./../config');
const logger = require('./../logger');
const mongoose = require('mongoose');
const Utils = require('./../Utils');
const secretConfig = require('./../../config');
const log = logger(config.logger);

const { AUTH_CONSTANTS: {
    AUTH_MODULE_ACTIONS: {
        LOYALTY_SERVICE: {
            REWARD
        }
    }
}, validateAction } = require('@shoutout-labs/authz-utils');

class RewardClaimHandler {

    static async claimReward(organizationId, callerId, payload, ability, boundary) {
        try {
            const validatedObj = await ClaimRewardValidator.isValid(payload);

            let reward, rewardRedemptionLog;
            if (validatedObj.voucherCode) {
                const rewardVoucher = await RewardVoucherDAO.getVoucher(organizationId, {
                    voucherCode: Utils.encrypt(validatedObj.voucherCode, secretConfig.DATA_ENCRYPTION_SECRET),
                });

                if (!rewardVoucher) {
                    return Promise.reject(new CustomHttpError('invalid voucher code', 400));
                }

                const { status } = rewardVoucher;
                if (status === VOUCHER_STATUS.CLAIMED) {
                    return Promise.reject(new CustomHttpError('reward voucher already claimed', '400'));
                }
                rewardRedemptionLog = await RewardRedemptionLogsDAO.getRewardRedemptionLog(organizationId, rewardVoucher.rewardRedemptionLogId?.toString());
                reward = await RewardDAO.getReward(organizationId, rewardRedemptionLog.rewardId.toString());
            } else if (validatedObj.redemptionLogId) {
                rewardRedemptionLog = await RewardRedemptionLogsDAO.getRewardRedemptionLog(organizationId, validatedObj.redemptionLogId);
                if (!rewardRedemptionLog) {
                    return Promise.reject(new CustomHttpError('Reward redemption log not found', '404'));
                }
                reward = await RewardDAO.getReward(organizationId, rewardRedemptionLog.rewardId.toString());
                if (reward.subType === SUB_TYPE.VOUCHER) {
                    return Promise.reject(new CustomHttpError('voucher code is required to claim a voucher reward.', 400));
                }
            }

            const redemptionLogId = rewardRedemptionLog._id.toString();

            if (ability && boundary !== BOUNDARY.ROOT) {
                if (boundary === BOUNDARY.MERCHANT && !rewardRedemptionLog.merchantId) return Promise.reject(new CustomHttpError('unauthorized action', '403'));
                const claimLocation = await MerchantLocationsDAO.getMerchantLocation(rewardRedemptionLog.metadata?.claimLocationId, organizationId);
                if (!claimLocation) return Promise.reject(new CustomHttpError('claim location not found', '404'));
                await validateAction(REWARD.ACTIONS.CLAIM_REWARD, REWARD.MODULE_ID, rewardRedemptionLog.regionId?.toString(), boundary, ability, rewardRedemptionLog.merchantId?.toString(), claimLocation._id?.toString());
            }

            if (rewardRedemptionLog.status === STATUS.CANCELLED) {
                return Promise.reject(new CustomHttpError('reward redemption was cancelled', '400'));
            }

            if (rewardRedemptionLog.status === STATUS.CLAIMED) {
                return Promise.reject(new CustomHttpError('reward already claimed', '400'));
            }

            if (rewardRedemptionLog.status !== STATUS.READY) {
                return Promise.reject(new CustomHttpError('reward is not ready yet', '400'));
            }

            const { name, description, type, points, imageUrls } = reward;
            const redemptionLogUpdate = {
                status: STATUS.CLAIMED
            }

            const rewardVoucher = await RewardVoucherDAO.getVoucher(organizationId, {
                rewardRedemptionLogId: mongoose.Types.ObjectId(redemptionLogId)
            });
            const { _id, voucherCode, status } = rewardVoucher;

            if (status === VOUCHER_STATUS.CLAIMED) {
                return Promise.reject(new CustomHttpError('reward voucher already claimed', '400'));
            }

            const updatedVoucher = await RewardVoucherDAO.updateVoucher({
                status: VOUCHER_STATUS.CLAIMED
            }, _id.toString(), organizationId, {
                rewardRedemptionLogId: mongoose.Types.ObjectId(redemptionLogId),
                status: VOUCHER_STATUS.ISSUED
            }, callerId);
            await RewardRedemptionLogsDAO.updateRedemptionLog(redemptionLogUpdate, redemptionLogId, organizationId);
            await RewardDAO.updateRewardWithIncrement({
                claimedCount: 1
            }, rewardRedemptionLog.rewardId.toString(), organizationId, callerId);

            //reward log item id
            const activityData = {
                reward_name: name,
                reward_id: rewardRedemptionLog.rewardId.toString(),
                voucher_code: voucherCode //NOT DECRYPTED
            };

            await Shoutout.produceActivityToTopic({
                memberId: rewardRedemptionLog.memberId.toString(),
                activityName: reward.subType === SUB_TYPE.PARTNER ? `Claim ${reward.name}` : 'Claim Reward',
                organizationId,
                regionId: rewardRedemptionLog.regionId,
                createdOn: new Date(),
                activityData
            });
            return Promise.resolve({
                rewardId: rewardRedemptionLog.rewardId,
                voucherCode: voucherCode,
                status: updatedVoucher.status,
                reward: {
                    name, description, type, points, imageUrls
                }
            });
        } catch (e) {
            log.error(e);
            return Promise.reject(e);
        }
    }

}

module.exports = RewardClaimHandler;
