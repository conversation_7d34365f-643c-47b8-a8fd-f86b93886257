'use strict';
const CardDAO = require('./../db/dao/CardDAO');
const CardsAnalyticsDAO = require('./../db/dao/analytics/CardsAnalyticsDAO');
const _ = require('lodash');
const mongoose = require('mongoose');
const CardConfigurationsDAO = require('./../db/dao/CardConfigurationsDAO');
const MerchantLocationsDAO = require('./../db/dao/MerchantLocationsDAO');
const OrganizationDAO = require('./../db/dao/OrganizationDAO');
const RegionDAO = require('./../db/dao/RegionDAO');
const config = require('./../config');
const logger = require('./../logger');
const log = logger(config.logger);
const Utils = require('./../../lib/Utils');
const CardsValidator = require('../validators/CardsValidator');
const {
    CARD_STATUS,
    CARD_TYPES,
    CARD_PROCESSING_STATUS,
    CARD_CONFIGURATION_TYPES
} = require('../db/models/enums/card.enums');
const { Type: MemberType, Status } = require('../db/models/enums/member.enums');
const Card = require('../db/models/card.model');
const { BOUNDARY, OPERATION } = require('../db/models/enums/user.enums');
const MemberDAO = require('./../db/dao/MembersDAO');
const CustomHttpError = require('./../CustomHttpError');
const PointsHandler = require('./../handlers/PointsHandler');
const Shoutout = require('./../../lib/services/Shoutout');
const {
    AUTH_CONSTANTS: {
        AUTH_MODULE_ACTIONS: {
            LOYALTY_SERVICE: { CARD }
        }
    },
    validateAction
} = require('@shoutout-labs/authz-utils');
const { addJob: addCardStockRefreshJob } = require('./../../workers/processors/card.stock.view.refresh.job.processor');
const RedisConnector = require('./../db/connectors/RedisConnector');
const { generateARandomNumber } = require('../utils/NumberUtils');
const { delAsync } = RedisConnector.getCommands();

const getEventUpdateObj = (callerId, eventDetails, setObj) => {
    return {
        $set: setObj,
        $push: {
            historyEvents: {
                $each: [
                    {
                        eventDate: new Date(),
                        eventDetails,
                        eventBy: callerId
                    }
                ],
                $sort: { eventDate: -1 }
            }
        },
        $currentDate: { modifiedOn: true },
        updatedBy: callerId
    };
};

const cardReplacementPointDeduction = async (
    cardRegion,
    cardType,
    organizationId,
    memberRegion,
    memberId,
    memberPoints,
    memberCardReplacementCount,
    callerId
) => {
    const cardConfiguration = await CardConfigurationsDAO.getCardConfiguration(
        organizationId,
        cardRegion,
        CardsHandler.getConfigurationType(cardType)
    );
    const replacementFee = cardConfiguration.replacementFee;

    const {
        subtransactionTypeIdMap: { cardReplacement }
    } = await OrganizationDAO.getOrganization(organizationId);
    const { defaultMerchantId, defaultMerchantLocationId } = await RegionDAO.getRegion(memberRegion);
    const adjustmentObj = {
        memberId: memberId,
        merchantId: defaultMerchantId.toString(),
        merchantLocationId: defaultMerchantLocationId.toString(),
        pointsAmount: replacementFee,
        transactionDate: new Date(),
        transactionSubTypeId: cardReplacement.toString()
    };

    if (memberPoints === 0) {
        if (memberCardReplacementCount > 0) {
            return Promise.reject(new CustomHttpError('free card replacement limit reached', '400'));
        }
    } else if (replacementFee > memberPoints) {
        await PointsHandler.adjustPoints(
            organizationId,
            { ...adjustmentObj, pointsAmount: memberPoints },
            callerId,
            null,
            null,
            true
        );
    } else {
        await PointsHandler.adjustPoints(organizationId, adjustmentObj, callerId, null, null, true);
    }
    await MemberDAO.updateMember(
        {
            $inc: {
                cardReplacementCount: 1
            },
            $currentDate: { modifiedOn: true }
        },
        memberId,
        organizationId,
        callerId
    );
};

const waiveCardReplacementFee = async (oldCards, member, validatedDataObj, existingCard, organizationId, callerId) => {
    if (existingCard.type !== CARD_TYPES.DIGITAL_CARD) {
        if (member.type === MemberType.SECONDARY) {
            if (!validatedDataObj?.waiveCardReplacementFee) {
                const primaryMember = await MemberDAO.getMemberById(member.parentMemberId.toString(), organizationId);
                await cardReplacementPointDeduction(
                    existingCard.regionId,
                    _.first(oldCards).type,
                    organizationId,
                    primaryMember.regionId.toString(),
                    primaryMember._id.toString(),
                    primaryMember.points,
                    member.cardReplacementCount,
                    callerId
                );
            }
        } else {
            if (!validatedDataObj?.waiveCardReplacementFee) {
                await cardReplacementPointDeduction(
                    existingCard.regionId,
                    _.first(oldCards).type,
                    organizationId,
                    member.regionId.toString(),
                    member._id.toString(),
                    member.points,
                    member.cardReplacementCount,
                    callerId
                );
            }
        }
    }
};

const invalidateCardNumberCache = async (organizationId, memberId, cardNumber) => {
    try {
        await delAsync(`members:cards:${organizationId}:${memberId}`);
        await delAsync(`cards:${organizationId}:${cardNumber}`);
    } catch (e) {
        log.error('error while invalidating card cache\n', e);
    }
};

const autoGenerateAndValidateNumber = async (
    { organizationId, loyaltyCardNumberLength },
    validateNumberRetryCount = 0
) => {
    try {
        if (validateNumberRetryCount > 4)
            throw new CustomHttpError(
                'Exceeded maximum retries auto generating a random number for a card number.',
                '400',
                '180003'
            );

        const autoGeneratedString = generateARandomNumber(loyaltyCardNumberLength).toString();
        log.debug('Auto generated string:', autoGeneratedString);

        const existingCard = await CardDAO.validateCardNumbersUniqueness([autoGeneratedString], organizationId);

        if (!existingCard) return autoGeneratedString;

        validateNumberRetryCount++;
        log.info(`Auto generated string '${autoGeneratedString}' already exists as a card number.`);
        log.info('Retry count', validateNumberRetryCount);

        await autoGenerateAndValidateNumber({ organizationId, loyaltyCardNumberLength }, validateNumberRetryCount);
    } catch (err) {
        log.error('Failed to validate auto generated number', err);
        throw err;
    }
};

class CardsHandler {
    static async getCards(organizationId, validatedObj, exportFile = false, ability, boundary) {
        try {
            if (ability) {
                await validateAction(
                    CARD.ACTIONS.LIST_CARDS,
                    CARD.MODULE_ID,
                    validatedObj.regionId,
                    boundary,
                    ability,
                    null,
                    null,
                    false,
                    true
                );
            }
            let projection;
            if (validatedObj.fields && validatedObj.fields.length > 0) {
                projection = Utils.buildProjection(validatedObj.fields);
            }

            if (exportFile) {
                const cursor = await CardDAO.getCards(organizationId, validatedObj, projection, true);
                const headers = [
                    { id: 'cardNo', title: 'CARD_NO' },
                    { id: 'printedName', title: 'PRINTED_NAME' }
                ];
                const exportResult = await Utils.exportToCSV(
                    cursor,
                    organizationId,
                    'cards',
                    headers,
                    'cards',
                    true,
                    async (dataCursor, csvWriter) => {
                        for (let doc = await cursor.next(); doc != null; doc = await cursor.next()) {
                            const csvData = [
                                {
                                    cardNo: doc.cardNo,
                                    printedName: doc.embossCard.printedName
                                }
                            ];
                            await csvWriter.writeRecords(csvData);
                        }
                    }
                );
                return Promise.resolve(exportResult);
            }

            const cardsGetResult = await CardDAO.getCards(organizationId, validatedObj, projection);

            const merchantLocationIdsSet = new Set(),
                merchantLocationsMap = new Map();
            cardsGetResult.items.forEach((card) => {
                if (card.embossedCard?.merchantLocationId) {
                    merchantLocationIdsSet.add(card.embossedCard?.merchantLocationId);
                }
            });

            if (merchantLocationIdsSet.size > 0) {
                const merchantLocationsResult = await MerchantLocationsDAO.getMerchantLocationsByFilter(
                    organizationId,
                    {
                        _id: {
                            $in: Array.from(merchantLocationIdsSet).map((locationId) =>
                                mongoose.Types.ObjectId(locationId)
                            )
                        }
                    },
                    null,
                    { _id: 1, locationName: 1 }
                );

                merchantLocationsResult.forEach((location) => {
                    merchantLocationsMap.set(location._id.toString(), location.locationName);
                });

                for (const card of cardsGetResult.items) {
                    if (card.embossCard?.merchantLocationId)
                        card.embossCard.merchantLocationName = merchantLocationsMap.get(
                            card.embossCard?.merchantLocationId
                        );
                }
            }

            return Promise.resolve(cardsGetResult);
        } catch (error) {
            log.error('error\n', error);
            return Promise.reject(error);
        }
    }

    static async getCardById(id, organizationId, ability, boundary) {
        try {
            const result = await CardDAO.getCardById(id, organizationId);
            if (!result) {
                throw new CustomHttpError('Card not found', 404);
            }

            if (ability) {
                await validateAction(
                    CARD.ACTIONS.GET_CARD,
                    CARD.MODULE_ID,
                    result.regionId.toString(),
                    boundary,
                    ability
                );
            }

            if (result['embossCard'] !== undefined) {
                const merchantLocation = await MerchantLocationsDAO.getMerchantLocation(
                    result.embossCard.merchantLocationId,
                    organizationId
                );
                result.embossCard.merchantLocationName =
                    merchantLocation !== null ? merchantLocation.locationName : null;
            }
            return Promise.resolve(result);
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async generateCardsManually(dataObj, organizationId, userId, ability, boundary, opts = {}) {
        const auditData = {
            moduleId: CARD.MODULE_ID,
            action: CARD.ACTIONS.GENERATE_CARDS,
            operation: OPERATION.CREATE,
            auditMessage: 'Card generation successful'
        };

        try {
            const {
                configuration: {
                    cardConfiguration: { loyaltyCardNumberLength, allowManualCardGeneration }
                }
            } = await Utils.getOrganizationData(organizationId);
            if (!allowManualCardGeneration)
                throw new CustomHttpError('Manual card generation is not allowed', '400', '180001');

            const validatedDataObj = await CardsValidator.cardsManualGenerationValidation(dataObj);
            if (ability && boundary !== BOUNDARY.ROOT) {
                await validateAction(
                    CARD.ACTIONS.GENERATE_CARDS,
                    CARD.MODULE_ID,
                    validatedDataObj.regionId.toString(),
                    boundary,
                    ability
                );
            }

            let cardsList = [];
            if (validatedDataObj.autoGenerate) {
                // * Auto generate a number and validate.
                const toBeGeneratedCardNumber = await autoGenerateAndValidateNumber({
                    organizationId,
                    loyaltyCardNumberLength
                });

                cardsList.push(
                    new Card({
                        regionId: mongoose.Types.ObjectId(validatedDataObj.regionId),
                        cardNo: toBeGeneratedCardNumber,
                        cardNoStr: toBeGeneratedCardNumber.toString(),
                        organizationId: mongoose.Types.ObjectId(organizationId),
                        status: CARD_STATUS.ACTIVE,
                        processingStatus: CARD_PROCESSING_STATUS.COMPLETED,
                        type: CARD_TYPES.DIGITAL_CARD,
                        createdBy: mongoose.Types.ObjectId(userId),
                        historyEvents: [
                            {
                                eventDate: new Date(),
                                eventDetails: 'Auto generated card',
                                eventBy: mongoose.Types.ObjectId(userId)
                            }
                        ]
                    })
                );
            } else {
                const existingCards = await CardDAO.validateCardNumbersUniqueness(
                    validatedDataObj.cardNumbers,
                    organizationId
                );

                if (existingCards)
                    throw new CustomHttpError(
                        `The card number '${existingCards.cardNo}' is already in use`,
                        '400',
                        '180002'
                    );

                for (let cardNumber of validatedDataObj.cardNumbers) {
                    cardsList.push(
                        new Card({
                            regionId: mongoose.Types.ObjectId(validatedDataObj.regionId),
                            cardNo: cardNumber,
                            cardNoStr: cardNumber.toString(),
                            organizationId: mongoose.Types.ObjectId(organizationId),
                            status: CARD_STATUS.ACTIVE,
                            processingStatus: CARD_PROCESSING_STATUS.COMPLETED,
                            type: CARD_TYPES.DIGITAL_CARD,
                            createdBy: mongoose.Types.ObjectId(userId),
                            historyEvents: [
                                {
                                    eventDate: new Date(),
                                    eventDetails: 'Manually generated card',
                                    eventBy: mongoose.Types.ObjectId(userId)
                                }
                            ]
                        })
                    );
                }

                // TODO: REMOVE IF NOT NEEDED.
                // if (cardsList.length % secretConfig.WORKER_DB_REQUEST_CHUNK_SIZE === 0) {
                //     await CardDAO.createCardBatch(cardsList);
                //     cardsList = [];
                // }
                // TODO: REMOVE IF NOT NEEDED.
                // if (cardsList.length > 0) {
                //     await CardDAO.createCardBatch(cardsList);
                // }
            }

            const generatedCards = await CardDAO.createCardBatch(cardsList, opts);

            return Promise.resolve({ result: generatedCards, auditData: { ...auditData } });
        } catch (error) {
            error.errorCode = error?.errorCode || '180400';
            log.error('error\n', error);

            return Promise.reject({ err: error, auditData: { ...auditData, auditMessage: error.message } });
        }
    }

    static async updateCardAndCreateActivity(
        { organizationId, callerId, id, validatedDataObj, event },
        existingCard,
        { member = null, opts = {} }
    ) {
        if (validatedDataObj.status) {
            await this.validateStateChange(existingCard.status, validatedDataObj.status);
            event = `Card status changed from ${existingCard.status} to ${validatedDataObj.status}. `;
        }

        const eventObj = getEventUpdateObj(callerId, event, validatedDataObj);
        const cardUpdatedResult = await CardDAO.updateCard(eventObj, id, organizationId, callerId);

        if (existingCard.memberId) {
            if (!member) {
                member = await MemberDAO.getMemberById(existingCard.memberId, organizationId);
            }

            if (!member) {
                throw new CustomHttpError('Member not found', 404);
            }

            if (validatedDataObj.status === CARD_STATUS.DEACTIVATED) {
                const oldCards = await CardDAO.getCardsByFilter(
                    organizationId,
                    {
                        memberId: existingCard.memberId,
                        status: {
                            $in: [CARD_STATUS.ASSIGNED, CARD_STATUS.SUSPENDED, CARD_STATUS.DEACTIVATED]
                        }
                    },
                    { updatedOn: -1 },
                    null
                );
                if (oldCards.length > 0) {
                    await waiveCardReplacementFee(
                        oldCards,
                        member,
                        validatedDataObj,
                        existingCard,
                        organizationId,
                        callerId
                    );
                }
            }

            if (validatedDataObj.status === CARD_STATUS.ASSIGNED) {
                const memberUpdateEventObj = getEventUpdateObj(
                    callerId,
                    `Updated member with ${CARD_STATUS.ASSIGNED} card number`,
                    { cardNumber: existingCard.cardNoStr }
                );
                await MemberDAO.updateMember(
                    memberUpdateEventObj,
                    member._id.toString(),
                    organizationId,
                    callerId,
                    opts
                );
            } else {
                if (member.cardNumber === existingCard.cardNoStr) {
                    await MemberDAO.updateMember(
                        {
                            $set: {
                                cardNumber: null
                            },
                            $currentDate: { modifiedOn: true }
                        },
                        member._id.toString(),
                        organizationId,
                        callerId,
                        opts
                    );
                }
            }

            await invalidateCardNumberCache(organizationId, existingCard.memberId.toString(), existingCard.cardNo);

            try {
                await Shoutout.produceActivityToTopic({
                    memberId: existingCard.memberId.toString(),
                    activityName: `Loyalty Card ${_.startCase(_.lowerCase(validatedDataObj.status))}`,
                    organizationId,
                    regionId: existingCard.regionId.toString(),
                    createdOn: new Date(),
                    activityData: {
                        cardNo: existingCard.cardNo,
                        cardType: existingCard.type
                    }
                });
            } catch (error) {
                log.error('Failed create activity for card update', error);
            }
        }

        return cardUpdatedResult;
    }

    static async updateCard(
        dataObj,
        id,
        organizationId,
        callerId,
        ability,
        boundary,
        { member = null, existingCard = null, opts = {} }
    ) {
        const auditData = {
            moduleId: CARD.MODULE_ID,
            action: CARD.ACTIONS.UPDATE_CARD,
            operation: OPERATION.UPDATE,
            auditMessage: 'card update successful'
        };
        try {
            const validatedDataObj = await CardsValidator.cardsUpdateValidation(dataObj);

            if (!existingCard) {
                existingCard = await CardDAO.getCardById(id, organizationId);
            }

            if (!existingCard) {
                throw new CustomHttpError('Card not found', 404);
            }

            if (ability) {
                await validateAction(
                    CARD.ACTIONS.UPDATE_CARD,
                    CARD.MODULE_ID,
                    existingCard.regionId.toString(),
                    boundary,
                    ability
                );
            }

            if (existingCard.status === validatedDataObj.status) {
                throw new CustomHttpError(`The card is already in ${existingCard.status} state`, '400');
            }
            if (existingCard.status === CARD_STATUS.DEACTIVATED) {
                throw new CustomHttpError('Deactivated cards are non-reversible', '400');
            }

            if (validatedDataObj.status === CARD_STATUS.ASSIGNED) {
                const assignedCard = await CardDAO.getCard(organizationId, {
                    memberId: existingCard.memberId,
                    status: CARD_STATUS.ASSIGNED
                });
                if (assignedCard) {
                    throw new CustomHttpError('Another card has already been assigned to the associated member', '400');
                }
            }

            let event = 'Card details updated. ';
            if (validatedDataObj.processingStatus) {
                await this.validateProcessingStateChange(
                    existingCard.processingStatus,
                    validatedDataObj.processingStatus
                );
                event += `Card processing status changed from ${existingCard.processingStatus} to ${validatedDataObj.processingStatus}`;

                if (validatedDataObj.processingStatus === CARD_PROCESSING_STATUS.FAILED) {
                    validatedDataObj.status = CARD_STATUS.DEACTIVATED;
                }
            }

            if (validatedDataObj.embossCard?.embossIssuedOn) {
                validatedDataObj['embossCard.embossIssuedOn'] = validatedDataObj.embossCard?.embossIssuedOn;
                delete validatedDataObj.embossCard;
            }

            const cardUpdatedResult = await this.updateCardAndCreateActivity(
                { organizationId, callerId, id, validatedDataObj, event },
                existingCard,
                { member, opts }
            );

            try {
                addCardStockRefreshJob(organizationId, existingCard.regionId.toString());
            } catch (error) {
                log.error('Failed refresh card stock for card update', error);
            }

            return Promise.resolve({
                result: cardUpdatedResult,
                auditData
            });
        } catch (error) {
            log.error('error\n', error);
            const errorWithAuditLog = {
                err: error,
                auditData: { ...auditData, auditMessage: error.message }
            };
            return Promise.reject(errorWithAuditLog);
        }
    }

    static async cancelEmbossRequest(id, organizationId, callerId, ability, boundary) {
        const auditData = {
            moduleId: CARD.MODULE_ID,
            action: CARD.ACTIONS.CANCEL_EMBOSSED_REQUEST,
            operation: OPERATION.UPDATE,
            auditMessage: 'cancel emboss request successful'
        };
        try {
            const existingCard = await CardDAO.getCardById(id, organizationId);
            if (!existingCard) {
                throw new CustomHttpError('Card not found', 404);
            }

            if (ability) {
                await validateAction(
                    CARD.ACTIONS.CANCEL_EMBOSSED_REQUEST,
                    CARD.MODULE_ID,
                    existingCard.regionId.toString(),
                    boundary,
                    ability
                );
            }

            if (
                existingCard.type !== CARD_TYPES.EMBOSSED_CARD ||
                existingCard.processingStatus !== CARD_PROCESSING_STATUS.REQUESTED
            ) {
                throw new CustomHttpError('Card doesnt contain a emboss request', 400);
            }
            let eventObj = getEventUpdateObj(
                callerId,
                `Emboss request cancelled. Processing status changed from ${existingCard.processingStatus} to COMPLETED. Card type reversed to ${existingCard.embossCard.previousCardType}`,
                {
                    processingStatus: CARD_PROCESSING_STATUS.COMPLETED,
                    type: existingCard.embossCard.previousCardType
                }
            );
            const cardUpdatedResult = await CardDAO.updateCard(eventObj, id, organizationId, callerId);
            addCardStockRefreshJob(organizationId, existingCard.regionId.toString());
            return Promise.resolve({
                result: cardUpdatedResult,
                auditData
            });
        } catch (error) {
            log.error('error\n', error);
            const errorWithAuditLog = {
                err: error,
                auditData: { ...auditData, auditMessage: error.message }
            };
            return Promise.reject(errorWithAuditLog);
        }
    }

    static async requestEmbossedCard(dataObj, id, organizationId, callerId, ability, boundary) {
        const auditData = {
            moduleId: CARD.MODULE_ID,
            action: CARD.ACTIONS.CREATE_EMBOSSED_REQUEST,
            operation: OPERATION.UPDATE,
            auditMessage: 'request emboss card successful'
        };
        try {
            const validatedDataObj = await CardsValidator.cardsEmbossRequestValidation(dataObj);
            const existingCard = await CardDAO.getCardById(id, organizationId);

            if (!existingCard) {
                throw new CustomHttpError('Card not found', 404);
            }

            if (ability) {
                await validateAction(
                    CARD.ACTIONS.CREATE_EMBOSSED_REQUEST,
                    CARD.MODULE_ID,
                    existingCard.regionId.toString(),
                    boundary,
                    ability
                );
            }

            if (existingCard.status !== CARD_STATUS.ASSIGNED) {
                throw new CustomHttpError(`Couldn't request emboss card, card is in ${existingCard.status} state`, 400);
            }

            const merchantLocation = await MerchantLocationsDAO.getMerchantLocation(
                validatedDataObj.merchantLocationId,
                organizationId
            );
            if (!merchantLocation) {
                throw new CustomHttpError('Merchant location not found', '404');
            }

            const eventObj = getEventUpdateObj(
                callerId,
                `Embossed card requested, Card type changed from ${existingCard.type} to ${CARD_TYPES.EMBOSSED_CARD}, Processing status changed from ${existingCard.processingStatus} to ${CARD_PROCESSING_STATUS.REQUESTED}`,
                {
                    type: CARD_TYPES.EMBOSSED_CARD,
                    processingStatus: CARD_PROCESSING_STATUS.REQUESTED,
                    'embossCard.embossRequestedOn': new Date(),
                    'embossCard.printedName': validatedDataObj.printedName,
                    'embossCard.merchantId': merchantLocation.merchantId,
                    'embossCard.merchantLocationId': validatedDataObj.merchantLocationId,
                    'embossCard.requestedBy': callerId,
                    'embossCard.previousCardType': existingCard.type,
                    'embossCard.previousPrintJobId': existingCard.printJobId,
                    'embossCard.previousPrintJobNumber': existingCard.printJobNumber
                }
            );

            const cardUpdatedResult = await CardDAO.updateCard(eventObj, id, organizationId, callerId);
            addCardStockRefreshJob(organizationId, existingCard.regionId.toString());
            return Promise.resolve({
                result: cardUpdatedResult,
                auditData
            });
        } catch (error) {
            log.error('error\n', error);
            const errorWithAuditLog = {
                err: error,
                auditData: { ...auditData, auditMessage: error.message }
            };
            return Promise.reject(errorWithAuditLog);
        }
    }

    static async assignCardRequest(dataObj, organizationId, callerId, ability, boundary) {
        const auditData = {
            moduleId: CARD.MODULE_ID,
            action: CARD.ACTIONS.ASSIGN_CARD,
            operation: OPERATION.UPDATE,
            auditMessage: 'Assign card successful'
        };

        try {
            const cardUpdatedResult = await this.assignCard(dataObj, organizationId, callerId, ability, boundary, {
                member: null,
                opts: {}
            });

            return Promise.resolve({
                result: cardUpdatedResult,
                auditData
            });
        } catch (error) {
            error.errorCode = error?.errorCode || '190400';

            log.error('error\n', error);
            return Promise.reject({
                err: error,
                auditData: { ...auditData, auditMessage: error.message }
            });
        }
    }

    static async assignCardAndCreateActivity(
        { organizationId, callerId, validatedDataObj },
        existingCard,
        { member = null, opts = {} }
    ) {
        if (existingCard.status !== CARD_STATUS.ACTIVE) {
            throw new CustomHttpError(
                `Card must be ${CARD_STATUS.ACTIVE}, card is in ${existingCard.status} state`,
                '400',
                '190001'
            );
        }

        const oldCards = await CardDAO.getCardsByFilter(
            organizationId,
            {
                memberId: validatedDataObj.memberId,
                status: { $in: [CARD_STATUS.ASSIGNED, CARD_STATUS.SUSPENDED, CARD_STATUS.DEACTIVATED] }
            },
            { updatedOn: -1 },
            null
        );

        if (oldCards.length > 0) {
            await waiveCardReplacementFee(oldCards, member, validatedDataObj, existingCard, organizationId, callerId);

            const eventObj = getEventUpdateObj(callerId, 'Card deactivated and replaced', {
                status: CARD_STATUS.DEACTIVATED
            });
            await CardDAO.updateCardBulkStatus(eventObj, {
                organizationId,
                memberId: validatedDataObj.memberId,
                status: CARD_STATUS.ASSIGNED
            });
        }

        const cardUpdatedResult = await CardDAO.assignCard(
            existingCard._id,
            organizationId,
            callerId,
            validatedDataObj.memberId
        );

        // * Update member with assigned card number.
        const memberUpdateEventObj = getEventUpdateObj(
            callerId,
            `Updated member with ${CARD_STATUS.ASSIGNED} card number`,
            { cardNumber: existingCard.cardNoStr }
        );
        await MemberDAO.updateMember(memberUpdateEventObj, validatedDataObj.memberId, organizationId, callerId, opts);

        await invalidateCardNumberCache(organizationId, member._id.toString(), validatedDataObj.cardNoStr);

        try {
            await Shoutout.produceActivityToTopic({
                memberId: member._id.toString(),
                activityName: 'Assign Loyalty Card',
                organizationId,
                regionId: member.regionId.toString(),
                createdOn: new Date(),
                activityData: {
                    cardNo: validatedDataObj.cardNoStr,
                    cardType: existingCard.type
                }
            });
            addCardStockRefreshJob(organizationId, existingCard.regionId.toString());
        } catch (error) {
            log.error('Failed create activity or refresh card stock card assign', error);
        }

        return cardUpdatedResult;
    }

    static async assignCard(dataObj, organizationId, callerId, ability, boundary, { member = null, opts = {} }) {
        try {
            const validatedDataObj = await CardsValidator.cardsAssignValidation(dataObj);

            if (!validatedDataObj.cardNumberStr) {
                validatedDataObj.cardNumberStr = validatedDataObj.cardNumber.toString();
            }

            const existingCard = await CardDAO.getCard(organizationId, {
                organizationId,
                cardNoStr: validatedDataObj.cardNumberStr
            });

            if (!existingCard) {
                throw new CustomHttpError('Card not found', '404', '170001');
            }

            if (ability) {
                await validateAction(
                    CARD.ACTIONS.ASSIGN_CARD,
                    CARD.MODULE_ID,
                    existingCard.regionId.toString(),
                    boundary,
                    ability
                );
            }

            if (!member) {
                member = await MemberDAO.getMemberById(validatedDataObj.memberId, organizationId);
            }

            if (!member) {
                throw new CustomHttpError('Member not found', '404', '170002');
            }

            if (member.status === Status.SUSPENDED) {
                throw new CustomHttpError('Cards cannot be assigned to suspended users', '400', '170003');
            }

            const cardUpdatedResult = await this.assignCardAndCreateActivity(
                { organizationId, callerId, validatedDataObj },
                existingCard,
                { member, opts }
            );

            return cardUpdatedResult;
        } catch (error) {
            log.error('error\n', error);
            return Promise.reject(error);
        }
    }

    static async assignDigitalCard(dataObj, organizationId, callerId) {
        try {
            const { memberId } = await CardsValidator.digitalCardAssignValidation(dataObj);

            const member = await MemberDAO.getMemberById(memberId, organizationId);
            if (member.status === Status.SUSPENDED) {
                throw new CustomHttpError('Cards cannot be assigned to suspended users', 400);
            }

            const digitalCard = await CardDAO.getCard(organizationId, {
                type: CARD_TYPES.DIGITAL_CARD,
                status: CARD_STATUS.ACTIVE,
                regionId: member.regionId
            });

            if (!digitalCard) {
                return Promise.reject(new CustomHttpError('No active digital cards found', '400'));
            }

            const assignedCards = await CardDAO.getCardsByFilter(
                organizationId,
                {
                    memberId: memberId,
                    status: CARD_STATUS.ASSIGNED
                },
                { updatedOn: -1 },
                null
            );

            if (assignedCards.length > 0) {
                // TODO: REMOVE IF NOT NEEDED.
                // const cardConfiguration = await CardConfigurationsDAO.getCardConfiguration(organizationId, digitalCard.regionId, this.getConfigurationType(assignedCards[0].type));
                // const replacementFee = cardConfiguration.replacementFee;

                //TODO: DEDUCT REPLACEMENT FEE FROM POINTS

                let eventObj = getEventUpdateObj(callerId, 'Card deactivated and replaced', {
                    status: CARD_STATUS.DEACTIVATED
                });
                await CardDAO.updateCardBulkStatus(eventObj, {
                    organizationId,
                    loyaltyId: member.loyaltyId,
                    status: CARD_STATUS.ASSIGNED
                });
            }
            const cardUpdatedResult = await CardDAO.assignCard(digitalCard._id, organizationId, callerId, memberId);
            await MemberDAO.updateMember(
                {
                    $set: {
                        cardNumber: digitalCard.cardNoStr
                    },
                    $currentDate: { modifiedOn: true }
                },
                memberId,
                organizationId,
                callerId
            );

            await invalidateCardNumberCache(organizationId, memberId, digitalCard.cardNoStr);

            addCardStockRefreshJob(organizationId, digitalCard.regionId.toString());
            return Promise.resolve(cardUpdatedResult);
        } catch (error) {
            log.error('error\n', error);
            return Promise.reject(error);
        }
    }

    static getConfigurationType(cardType) {
        switch (cardType) {
            case CARD_TYPES.DIGITAL_CARD:
                return CARD_CONFIGURATION_TYPES.DIGITAL;
            case CARD_TYPES.EMBOSSED_CARD:
                return CARD_CONFIGURATION_TYPES.EMBOSSED;
            default:
                return CARD_CONFIGURATION_TYPES.INSTANT;
        }
    }

    static validateStateChange(currentState, newState) {
        switch (currentState) {
            case CARD_STATUS.PENDING:
            case CARD_STATUS.ACTIVE:
                if (newState !== CARD_STATUS.DEACTIVATED) {
                    return Promise.reject(new CustomHttpError('Valid states - DEACTIVATED', 400));
                }
                break;
            case CARD_STATUS.ASSIGNED:
                if (newState !== CARD_STATUS.DEACTIVATED && newState !== CARD_STATUS.SUSPENDED) {
                    return Promise.reject(new CustomHttpError('Valid states - SUSPENDED, DEACTIVATED', 400));
                }
                break;
            case CARD_STATUS.SUSPENDED:
                if (newState !== CARD_STATUS.DEACTIVATED && newState !== CARD_STATUS.ASSIGNED) {
                    return Promise.reject(new CustomHttpError('Valid states - ASSIGNED, DEACTIVATED', 400));
                }
                break;
            case CARD_STATUS.DEACTIVATED:
                return Promise.reject(new CustomHttpError('Deactivated cards are non-reversible', 400));
            default:
                return Promise.reject(
                    new CustomHttpError(`Invalid state change, Card is in ${currentState} state`, 400)
                );
        }
    }

    static validateProcessingStateChange(currentState, newState) {
        switch (currentState) {
            case CARD_PROCESSING_STATUS.REQUESTED:
                return Promise.reject(
                    new CustomHttpError('Card contains an emboss request, Cancel the emboss request first.', 400)
                );
            case CARD_PROCESSING_STATUS.FAILED:
                return Promise.reject(new CustomHttpError('Failed cards are non-reversible', 400));
            default:
                if (newState !== CARD_PROCESSING_STATUS.FAILED) {
                    return Promise.reject(
                        new CustomHttpError(`Processing state is in ${currentState} state, Valid states - FAILED`, 400)
                    );
                }
        }
    }

    static async getCardsSummary(organizationId, validatedObj, ability, boundary) {
        try {
            if (ability) {
                await validateAction(CARD.ACTIONS.LIST_CARDS, CARD.MODULE_ID, validatedObj.regionId, boundary, ability);
            }
            const cardsGetResult = await CardsAnalyticsDAO.getCardsSummary(organizationId, validatedObj);
            return Promise.resolve(cardsGetResult);
        } catch (error) {
            log.error('error\n', error);
            return Promise.reject(error);
        }
    }

    static async getCardsStock(organizationId, validatedObj, ability, boundary) {
        try {
            if (ability) {
                await validateAction(CARD.ACTIONS.LIST_CARDS, CARD.MODULE_ID, validatedObj.regionId, boundary, ability);
            }
            const cardsGetResult = await CardsAnalyticsDAO.getCardsStock(organizationId, validatedObj);
            return Promise.resolve(cardsGetResult);
        } catch (error) {
            log.error('error\n', error);
            return Promise.reject(error);
        }
    }

    static async validateCard(cardNo, organizationId) {
        try {
            const result = await CardDAO.getCard(organizationId, {
                cardNoStr: cardNo,
                status: CARD_STATUS.ASSIGNED
            });
            if (result) {
                return Promise.resolve(result);
            }
            return Promise.reject(new CustomHttpError('Invalid card', '400'));
        } catch (error) {
            log.error('error\n', error);
            return Promise.reject(error);
        }
    }
}

module.exports = CardsHandler;
